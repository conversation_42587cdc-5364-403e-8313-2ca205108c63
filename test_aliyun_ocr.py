#!/usr/bin/env python3
"""
阿里云OCR验证码识别测试脚本
"""

import base64
import os
import requests
import hashlib
import hmac
import urllib.parse
from datetime import datetime, timezone
import uuid
import json

def test_aliyun_ocr_captcha(image_path, access_key_id, access_key_secret):
    """
    测试阿里云OCR识别验证码
    Args:
        image_path: 验证码图片路径
        access_key_id: 阿里云AccessKey ID
        access_key_secret: 阿里云AccessKey Secret
    Returns:
        str: 识别结果
    """
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return None
    
    print(f"🔍 开始测试阿里云OCR识别验证码: {image_path}")
    
    try:
        # 读取图片并转换为base64
        with open(image_path, 'rb') as image_file:
            image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        
        print(f"✅ 图片已转换为base64，长度: {len(image_base64)} 字符")
        
        # API参数
        action = "RecognizeAllText"
        version = "2021-07-07"
        region = "cn-hangzhou"
        endpoint = "https://ocr-api.cn-hangzhou.aliyuncs.com"
        
        # 请求参数
        params = {
            "Action": action,
            "Version": version,
            "RegionId": region,
            "Format": "JSON",
            "AccessKeyId": access_key_id,
            "SignatureMethod": "HMAC-SHA1",
            "SignatureVersion": "1.0",
            "SignatureNonce": str(uuid.uuid4()),
            "Timestamp": datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ'),
            "Body": image_base64
        }
        
        print("🔐 生成API签名...")
        
        # 生成签名
        signature = generate_aliyun_signature(params, access_key_secret)
        params["Signature"] = signature
        
        print("🌐 调用阿里云OCR API...")
        
        # 发送请求
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "User-Agent": "AlibabaCloud (Windows; x64) Python/3.x Core/1.x"
        }
        
        response = requests.post(f"{endpoint}/", data=params, headers=headers, timeout=30)
        
        print(f"📡 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 API响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if "Data" in result and "Content" in result["Data"]:
                recognized_text = result["Data"]["Content"]
                print(f"🎯 识别原始结果: '{recognized_text}'")
                
                # 清理结果
                cleaned_result = clean_ocr_result(recognized_text)
                print(f"✨ 清理后的结果: '{cleaned_result}'")
                
                return cleaned_result
            else:
                print(f"❌ API返回错误: {result}")
                return None
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 阿里云OCR识别失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_aliyun_signature(params, access_key_secret):
    """
    生成阿里云API签名
    Args:
        params: 请求参数字典
        access_key_secret: AccessKey Secret
    Returns:
        str: 签名字符串
    """
    try:
        # 排序参数
        sorted_params = sorted(params.items())
        
        # 构造规范化请求字符串
        query_string = "&".join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in sorted_params])
        
        # 构造待签名字符串
        string_to_sign = f"POST&%2F&{urllib.parse.quote_plus(query_string)}"
        
        # 计算签名
        signature = hmac.new(
            (access_key_secret + "&").encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha1
        ).digest()
        
        return base64.b64encode(signature).decode('utf-8')
        
    except Exception as e:
        print(f"❌ 生成签名失败: {e}")
        return ""

def clean_ocr_result(text):
    """清理OCR识别结果"""
    if not text:
        return ""
    
    # 移除空格和换行符
    text = text.strip().replace('\n', '').replace('\r', '').replace(' ', '')
    
    # 只保留字母数字
    cleaned = ''.join(char for char in text if char.isalnum())
    
    # 常见字符混淆修正
    replacements = {
        'O': '0', 'o': '0', 'I': '1', 'l': '1',
        'S': '5', 'G': '6', 'B': '8', 'Z': '2'
    }
    
    if len(cleaned) >= 3 and cleaned.isalnum():
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
    
    return cleaned.upper()

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 阿里云OCR验证码识别测试工具")
    print("=" * 60)
    
    # 获取用户输入的AccessKey
    print("请输入您的阿里云AccessKey信息:")
    access_key_id = input("AccessKey ID: ").strip()
    access_key_secret = input("AccessKey Secret: ").strip()
    
    if not access_key_id or not access_key_secret:
        print("❌ AccessKey信息不能为空")
        return
    
    # 测试指定的验证码图片
    image_path = "captcha_selenium_1753772999.png"
    
    result = test_aliyun_ocr_captcha(image_path, access_key_id, access_key_secret)
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if result:
        print(f"✅ 识别成功: {result}")
        print(f"📏 结果长度: {len(result)} 字符")
        print(f"🔤 字符类型: {'数字' if result.isdigit() else '字母数字混合' if result.isalnum() else '包含特殊字符'}")
    else:
        print("❌ 识别失败")
    
    print("\n💡 提示:")
    print("- 确保AccessKey有OCR服务权限")
    print("- 检查账户余额是否充足")
    print("- 验证码图片质量会影响识别准确率")

if __name__ == "__main__":
    main()
