#!/usr/bin/env python3
"""
OCR验证码识别测试脚本
用于测试验证码OCR识别功能
"""

import os
import sys
import time
from PIL import Image, ImageDraw, ImageFont
import random
import string

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from selenium_login_solution import SeleniumLoginSolution
    print("✅ 成功导入SeleniumLoginSolution")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def create_test_captcha(text="A3B7", filename="test_captcha.png"):
    """
    创建测试验证码图片
    Args:
        text: 验证码文本
        filename: 保存的文件名
    """
    try:
        # 创建图片
        width, height = 120, 40
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试使用系统字体
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 20)
            except:
                font = ImageFont.load_default()
        
        # 绘制文本
        text_width = draw.textlength(text, font=font)
        x = (width - text_width) // 2
        y = (height - 20) // 2
        
        # 添加一些干扰
        for _ in range(20):
            x1 = random.randint(0, width)
            y1 = random.randint(0, height)
            x2 = random.randint(0, width)
            y2 = random.randint(0, height)
            draw.line([(x1, y1), (x2, y2)], fill='lightgray', width=1)
        
        # 绘制验证码文本
        draw.text((x, y), text, fill='black', font=font)
        
        # 保存图片
        image.save(filename)
        print(f"✅ 测试验证码已创建: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ 创建测试验证码失败: {e}")
        return None

def test_ocr_recognition():
    """测试OCR识别功能"""
    print("=" * 60)
    print("🧪 OCR验证码识别功能测试")
    print("=" * 60)
    
    # 创建测试实例（不启动浏览器）
    try:
        # 只测试OCR功能，不需要启动浏览器
        test_instance = SeleniumLoginSolution.__new__(SeleniumLoginSolution)
        test_instance.driver = None
        test_instance.wait = None
        test_instance.headless = True
        test_instance.ocr_enabled = True
        
        # 初始化OCR
        try:
            import easyocr
            test_instance.ocr_reader = easyocr.Reader(['en', 'ch_sim'], gpu=False)
            print("✅ OCR识别器初始化成功")
        except ImportError:
            print("❌ EasyOCR未安装，请运行: pip install easyocr")
            return False
        except Exception as e:
            print(f"❌ OCR初始化失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试实例创建失败: {e}")
        return False
    
    # 测试用例
    test_cases = [
        "A3B7",
        "X9Y2",
        "M5N8",
        "P4Q1",
        "2468"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    print(f"\n开始测试 {total_tests} 个验证码...")
    print()
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"测试 {i}/{total_tests}: {test_text}")
        
        # 创建测试验证码
        test_filename = f"test_captcha_{i}.png"
        if not create_test_captcha(test_text, test_filename):
            continue
        
        # OCR识别
        try:
            recognized_text = test_instance.recognize_captcha_with_ocr(test_filename)
            
            if recognized_text:
                if recognized_text.upper() == test_text.upper():
                    print(f"  ✅ 识别成功: {test_text} -> {recognized_text}")
                    success_count += 1
                else:
                    print(f"  ❌ 识别错误: {test_text} -> {recognized_text}")
            else:
                print(f"  ❌ 识别失败: {test_text} -> None")
                
        except Exception as e:
            print(f"  ❌ 识别异常: {e}")
        
        # 清理测试文件
        try:
            os.remove(test_filename)
        except:
            pass
        
        print()
    
    # 输出测试结果
    print("=" * 60)
    print("📊 测试结果统计")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"成功识别: {success_count}")
    print(f"识别准确率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！OCR功能工作正常")
        return True
    elif success_count > total_tests * 0.7:
        print("✅ 大部分测试通过，OCR功能基本正常")
        return True
    else:
        print("⚠️  测试通过率较低，建议检查OCR配置")
        return False

def main():
    """主函数"""
    print("🔍 OCR验证码识别测试工具")
    print()
    
    # 检查依赖
    try:
        import easyocr
        import cv2
        import numpy as np
        from PIL import Image
        print("✅ 所有依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: python install_ocr_dependencies.py")
        return
    
    print()
    
    # 运行测试
    if test_ocr_recognition():
        print("\n🎯 建议:")
        print("  - OCR功能已就绪，可以在实际登录中使用")
        print("  - 如果实际验证码识别率不高，可能需要调整预处理参数")
    else:
        print("\n🔧 建议:")
        print("  - 检查OCR库安装是否完整")
        print("  - 尝试重新安装依赖: pip install easyocr opencv-python")

if __name__ == "__main__":
    main()
