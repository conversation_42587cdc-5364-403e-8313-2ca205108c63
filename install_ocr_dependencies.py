#!/usr/bin/env python3
"""
ddddocr验证码识别依赖安装脚本
自动安装ddddocr验证码识别所需的Python库
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 ddddocr验证码识别依赖安装工具")
    print("=" * 60)

    # 需要安装的包列表
    required_packages = [
        ("ddddocr", "ddddocr - 专业验证码识别库"),
        ("opencv-python", "OpenCV - 图像处理库"),
        ("Pillow", "PIL - Python图像库"),
        ("numpy", "NumPy - 数值计算库"),
    ]
    
    print("检查当前环境...")
    print()
    
    # 检查已安装的包
    installed_packages = []
    missing_packages = []
    
    for package, description in required_packages:
        if check_package(package.replace("-", "_")):  # 处理包名差异
            installed_packages.append(package)
        else:
            missing_packages.append((package, description))
    
    print()
    
    if not missing_packages:
        print("🎉 所有依赖都已安装！")
        print("您可以直接运行 selenium_login_solution.py 使用ddddocr验证码识别功能")
        return
    
    print(f"需要安装 {len(missing_packages)} 个依赖包:")
    for package, description in missing_packages:
        print(f"  - {package}: {description}")
    
    print()
    
    # 询问用户是否继续安装
    response = input("是否现在安装这些依赖？(y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("安装已取消")
        return
    
    print()
    print("开始安装依赖...")
    print()
    
    # 安装缺失的包
    success_count = 0
    for package, description in missing_packages:
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 60)
    
    if success_count == len(missing_packages):
        print("🎉 所有依赖安装完成！")
        print()
        print("现在您可以运行以下命令测试阿里云OCR验证码识别功能:")
        print("  python test_aliyun_ocr.py")
        print("  python selenium_login_solution.py")
        print()
        print("阿里云OCR验证码识别功能说明:")
        print("  ✅ 使用阿里云OCR自动识别验证码图片")
        print("  ✅ 图像预处理提高识别率")
        print("  ✅ 识别失败时自动回退到手动输入")
        print("  ✅ 支持中英文验证码")
        print("  ✅ 基于阿里云OCR API，识别准确率高")
        print()
        print("⚠️  重要提醒:")
        print("  1. 需要在阿里云控制台开通OCR服务")
        print("  2. 需要创建AccessKey并配置到代码中")
        print("  3. 确保账户有足够余额（按次计费）")
    else:
        failed_count = len(missing_packages) - success_count
        print(f"⚠️  {failed_count} 个依赖安装失败")
        print("请检查网络连接或手动安装失败的依赖")
        print()
        print("手动安装命令:")
        for package, _ in missing_packages:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
