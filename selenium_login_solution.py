"""
私域网站爬虫 - Selenium自动化浏览器解决方案
基于真实浏览器环境，完全绕过反爬虫检测
集成ddddocr自动识别验证码
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import time
import requests
import os
import base64
from PIL import Image, ImageEnhance, ImageFilter
import io
import cv2
import numpy as np
import logging

# 验证码识别相关导入
DDDDOCR_AVAILABLE = False
PYTESSERACT_AVAILABLE = False

# 尝试导入ddddocr
try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
    print("[+] ddddocr库已加载，将使用ddddocr识别验证码")
except ImportError as e:
    print(f"[-] ddddocr库加载失败: {e}")

# 如果ddddocr不可用，尝试pytesseract
if not DDDDOCR_AVAILABLE:
    try:
        import pytesseract
        PYTESSERACT_AVAILABLE = True
        print("[+] pytesseract库已加载，将使用pytesseract识别验证码")
    except ImportError:
        print("[-] pytesseract库未安装，请运行: pip install pytesseract")

if not DDDDOCR_AVAILABLE and not PYTESSERACT_AVAILABLE:
    print("[-] 所有OCR库都不可用，将回退到手动输入验证码模式")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SeleniumLoginSolution:
    def __init__(self, headless=False, ddddocr_enabled=True):
        """
        初始化Selenium浏览器和ddddocr验证码识别器
        Args:
            headless: 是否使用无头模式（True=后台运行，False=显示浏览器窗口）
            ddddocr_enabled: 是否启用ddddocr自动识别验证码（True=自动识别，False=手动输入）
        """
        self.driver = None
        self.wait = None
        self.headless = headless
        self.ocr_enabled = ddddocr_enabled and (DDDDOCR_AVAILABLE or PYTESSERACT_AVAILABLE)
        self.ocr = None
        self.ocr_type = None

        # 初始化OCR识别器
        if self.ocr_enabled:
            if DDDDOCR_AVAILABLE:
                try:
                    self.ocr = ddddocr.DdddOcr()
                    self.ocr_type = "ddddocr"
                    logging.info("ddddocr验证码识别器初始化成功")
                except Exception as e:
                    logging.error(f"ddddocr识别器初始化失败: {e}")
                    self.ocr_enabled = False
            elif PYTESSERACT_AVAILABLE:
                try:
                    # pytesseract不需要特殊初始化
                    self.ocr_type = "pytesseract"
                    logging.info("pytesseract验证码识别器初始化成功")
                except Exception as e:
                    logging.error(f"pytesseract识别器初始化失败: {e}")
                    self.ocr_enabled = False

        if not self.ocr_enabled:
            logging.warning("OCR验证码识别功能未启用，将使用手动输入验证码模式")

        self.setup_driver()
    
    def setup_driver(self):
        """配置Chrome浏览器"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
        
        # 反检测配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 窗口大小
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            print("[+] Chrome浏览器启动成功")
        except Exception as e:
            print(f"[-] 浏览器启动失败: {e}")
            print("请确保已安装Chrome浏览器")
            print("如果问题持续，请运行: python install_dependencies.py")
            raise

    def preprocess_captcha_image(self, image_path):
        """
        验证码图片预处理，提高OCR识别准确率
        Args:
            image_path: 验证码图片路径
        Returns:
            str: 预处理后的图片路径
        """
        try:
            logging.info(f"开始预处理验证码图片: {image_path}")

            # 使用PIL打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 调整图片大小，放大以提高识别率
                width, height = img.size
                if width < 200 or height < 80:
                    scale_factor = max(200/width, 80/height)
                    new_width = int(width * scale_factor)
                    new_height = int(height * scale_factor)
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logging.info(f"图片已放大: {width}x{height} -> {new_width}x{new_height}")

                # 增强对比度
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(2.0)  # 增强对比度

                # 增强锐度
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(2.0)  # 增强锐度

                # 转换为numpy数组进行OpenCV处理
                img_array = np.array(img)

                # 转换为灰度图
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

                # 高斯模糊去噪
                blurred = cv2.GaussianBlur(gray, (3, 3), 0)

                # 自适应阈值二值化
                binary = cv2.adaptiveThreshold(
                    blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY, 11, 2
                )

                # 形态学操作去除噪点
                kernel = np.ones((2, 2), np.uint8)
                cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
                cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

                # 保存预处理后的图片
                processed_path = image_path.replace('.png', '_processed.png')
                cv2.imwrite(processed_path, cleaned)

                logging.info(f"图片预处理完成: {processed_path}")
                return processed_path

        except Exception as e:
            logging.error(f"图片预处理失败: {e}")
            return image_path  # 返回原图片路径

    def recognize_captcha_with_ocr(self, image_path, max_attempts=3):
        """
        使用OCR识别验证码（支持ddddocr和pytesseract）
        Args:
            image_path: 验证码图片路径
            max_attempts: 最大尝试次数
        Returns:
            str: 识别出的验证码文本，失败返回None
        """
        if not self.ocr_enabled:
            logging.warning("OCR验证码识别功能未启用")
            return None

        try:
            logging.info(f"开始{self.ocr_type}识别验证码: {image_path}")

            best_result = None

            # 尝试多次识别
            for attempt in range(max_attempts):
                try:
                    logging.info(f"{self.ocr_type}识别尝试 {attempt + 1}/{max_attempts}")

                    if self.ocr_type == "ddddocr":
                        result = self._recognize_with_ddddocr(image_path)
                    elif self.ocr_type == "pytesseract":
                        result = self._recognize_with_pytesseract(image_path)
                    else:
                        logging.error(f"未知的OCR类型: {self.ocr_type}")
                        return None

                    if result:
                        # 清理识别结果
                        cleaned_result = self._clean_ocr_result(result)

                        logging.info(f"{self.ocr_type}识别结果 (尝试{attempt+1}): 原始='{result}', 清理后='{cleaned_result}'")

                        # 验证结果是否合理
                        if cleaned_result and 3 <= len(cleaned_result) <= 8:
                            best_result = cleaned_result
                            break

                except Exception as e:
                    logging.error(f"{self.ocr_type}识别尝试{attempt+1}失败: {e}")
                    continue

            if best_result:
                logging.info(f"{self.ocr_type}识别成功: '{best_result}'")
                return best_result
            else:
                logging.warning(f"{self.ocr_type}识别失败")
                return None

        except Exception as e:
            logging.error(f"{self.ocr_type}识别过程出错: {e}")
            return None

    def _recognize_with_ddddocr(self, image_path):
        """使用ddddocr识别"""
        with open(image_path, 'rb') as f:
            img_bytes = f.read()
        return self.ocr.classification(img_bytes)

    def _recognize_with_pytesseract(self, image_path):
        """使用pytesseract识别"""
        # 预处理图片
        processed_image_path = self.preprocess_captcha_image(image_path)

        # 使用pytesseract识别
        from PIL import Image
        import pytesseract

        img = Image.open(processed_image_path)
        # 配置pytesseract参数，只识别字母数字
        config = '--psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
        result = pytesseract.image_to_string(img, config=config).strip()

        # 清理预处理后的图片
        try:
            if processed_image_path != image_path and os.path.exists(processed_image_path):
                os.remove(processed_image_path)
        except:
            pass

        return result

    def _clean_ocr_result(self, text):
        """
        清理OCR识别结果
        Args:
            text: 原始OCR识别文本
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""

        # 移除空格和换行符
        text = text.strip().replace('\n', '').replace('\r', '').replace(' ', '')

        # 只保留字母数字
        cleaned = ''.join(char for char in text if char.isalnum())

        # 常见字符混淆修正
        replacements = {
            'O': '0',  # 字母O替换为数字0
            'o': '0',  # 小写o替换为数字0
            'I': '1',  # 字母I替换为数字1
            'l': '1',  # 小写l替换为数字1
            'S': '5',  # 字母S有时会被误识别
            'G': '6',  # 字母G有时会被误识别
            'B': '8',  # 字母B有时会被误识别
            'Z': '2',  # 字母Z有时会被误识别为2
        }

        # 应用替换规则（仅当结果看起来像验证码时）
        if len(cleaned) >= 3 and cleaned.isalnum():
            for old, new in replacements.items():
                cleaned = cleaned.replace(old, new)

        return cleaned.upper()  # 转换为大写保持一致性


    
    def login(self, username="***********", password="aabb6688"):
        """
        执行自动登录
        Args:
            username: 用户名
            password: 密码
        Returns:
            bool: 登录是否成功
        """
        try:
            print("🚀 开始Selenium自动登录...")
            
            # 1. 访问登录页面
            login_url = "https://login-sso.siyscrm.com/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
            print(f"[+] 访问登录页面: {login_url}")
            self.driver.get(login_url)
            
            # 等待页面加载
            time.sleep(3)

            # 调试：打印页面标题确认页面加载
            print(f"[DEBUG] 页面标题: {self.driver.title}")
            print(f"[DEBUG] 当前URL: {self.driver.current_url}")
            
            # 2. 填写用户名
            print("[+] 填写用户名...")
            username_input = self.wait.until(EC.presence_of_element_located((By.ID, "userAccount")))
            username_input.clear()
            username_input.send_keys(username)
            
            # 3. 填写密码
            print("[+] 填写密码...")
            password_input = self.driver.find_element(By.ID, "pwd")
            password_input.clear()
            password_input.send_keys(password)
            
            # 4. 处理验证码
            print("[+] 处理验证码...")
            captcha_success = self.handle_captcha()
            if not captcha_success:
                print("[-] 验证码处理失败")
                return False
            
            # 5. 点击登录按钮
            print("[+] 点击登录按钮...")
            # 基于真实页面结构查找登录按钮
            try:
                login_button = self.driver.find_element(By.CSS_SELECTOR, "body > div > div > div > form > div:nth-of-type(1) > div:nth-of-type(6) > button")
                print("[+] 找到登录按钮")
            except NoSuchElementException:
                print("[-] 未找到登录按钮")
                return False

            login_button.click()
            
            # 6. 等待登录结果
            print("[+] 等待登录结果...")
            return self.wait_for_login_result()
            
        except TimeoutException:
            print("[-] 页面加载超时")
            return False
        except NoSuchElementException as e:
            print(f"[-] 页面元素未找到: {e}")
            return False
        except Exception as e:
            print(f"[-] 登录过程出错: {e}")
            return False
    
    def handle_captcha(self):
        """
        处理验证码 - 集成ddddocr自动识别功能
        Returns:
            bool: 验证码处理是否成功
        """
        try:
            logging.info("开始处理验证码...")
            # 等待页面完全加载
            time.sleep(2)

            # 基于真实页面结构查找验证码图片
            captcha_img = None
            try:
                captcha_img = self.driver.find_element(By.ID, "mixImg")
                logging.info("找到验证码图片元素: #mixImg")
            except NoSuchElementException:
                logging.error("未找到验证码图片元素")

            if not captcha_img:
                logging.error("未找到验证码图片元素")
                # 打印页面源码用于调试
                logging.info("页面中的img元素:")
                img_elements = self.driver.find_elements(By.TAG_NAME, "img")
                for i, img in enumerate(img_elements[:5]):  # 只显示前5个
                    try:
                        img_id = img.get_attribute('id') or 'None'
                        img_src = img.get_attribute('src') or 'None'
                        logging.info(f"  img[{i}]: id='{img_id}', src='{img_src[:100]}'")
                    except:
                        pass
                return False

            # 截取验证码图片
            captcha_screenshot = captcha_img.screenshot_as_png

            # 保存验证码图片
            captcha_filename = f"captcha_selenium_{int(time.time())}.png"
            with open(captcha_filename, 'wb') as f:
                f.write(captcha_screenshot)

            logging.info(f"验证码已保存: {captcha_filename}")

            # 尝试OCR自动识别
            captcha_code = None
            if self.ocr_enabled:
                logging.info(f"尝试使用{self.ocr_type}自动识别验证码...")
                captcha_code = self.recognize_captcha_with_ocr(captcha_filename)

                if captcha_code:
                    logging.info(f"{self.ocr_type}识别成功: {captcha_code}")
                else:
                    logging.warning(f"{self.ocr_type}识别失败，回退到手动输入模式")

            # 如果OCR识别失败或未启用，则手动输入
            if not captcha_code:
                print(f"\n验证码图片已保存: {captcha_filename}")
                print("请查看验证码图片并手动输入:")
                captcha_code = input("请输入验证码: ").strip()

            if not captcha_code:
                logging.error("验证码不能为空")
                return False

            # 基于真实页面结构查找验证码输入框
            try:
                captcha_input = self.driver.find_element(By.ID, "pictureVerifVode")
                logging.info("找到验证码输入框: #pictureVerifVode")
            except NoSuchElementException:
                logging.error("未找到验证码输入框")
                return False

            # 填写验证码
            captcha_input.clear()
            captcha_input.send_keys(captcha_code)
            logging.info(f"验证码已填入: {captcha_code}")

            # 清理验证码文件
            try:
                os.remove(captcha_filename)
                logging.info("已清理验证码文件")
            except Exception as e:
                logging.warning(f"清理验证码文件失败: {e}")

            return True

        except Exception as e:
            logging.error(f"验证码处理失败: {e}")
            return False
    
    def wait_for_login_result(self, timeout=15):
        """等待登录结果"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_url = self.driver.current_url
            
            # 检查是否跳转到目标页面
            if "apollo.siyscrm.com" in current_url and "login" not in current_url:
                print("🎉 登录成功!")
                print(f"[+] 当前URL: {current_url}")
                
                # 等待页面完全加载
                time.sleep(3)
                
                # 获取页面标题和内容
                page_title = self.driver.title
                print(f"[+] 页面标题: {page_title}")
                
                # 打印页面内容
                self.print_page_content()
                
                return True
            
            # 检查是否有错误信息
            try:
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error, .alert, .message")
                for element in error_elements:
                    if element.is_displayed() and element.text.strip():
                        print(f"[-] 登录错误: {element.text}")
                        return False
            except:
                pass
            
            time.sleep(1)
        
        print("[-] 登录超时")
        return False
    
    def print_page_content(self):
        """打印登录成功后的页面内容"""
        try:
            print("📄 登录成功后的页面内容:")
            print("=" * 80)
            
            # 获取页面文本内容
            body_text = self.driver.find_element(By.TAG_NAME, "body").text
            print(body_text[:1000])  # 打印前1000个字符
            
            print("=" * 80)
            
            # 获取主要导航菜单
            try:
                nav_elements = self.driver.find_elements(By.CSS_SELECTOR, "nav a, .nav a, .menu a, .sidebar a")
                if nav_elements:
                    print("🔗 主要功能菜单:")
                    for nav in nav_elements[:10]:  # 只显示前10个
                        if nav.text.strip():
                            print(f"  - {nav.text.strip()}")
            except:
                pass
            
        except Exception as e:
            print(f"[-] 获取页面内容失败: {e}")
    
    def get_cookies(self):
        """获取登录后的Cookies"""
        try:
            cookies = self.driver.get_cookies()
            print("🍪 登录Cookies:")
            for cookie in cookies:
                print(f"  {cookie['name']}: {cookie['value']}")
            return cookies
        except Exception as e:
            print(f"[-] 获取Cookies失败: {e}")
            return []
    
    def extract_session_for_requests(self):
        """提取会话信息用于requests库"""
        try:
            cookies = self.driver.get_cookies()
            session = requests.Session()
            
            # 添加cookies到requests session
            for cookie in cookies:
                session.cookies.set(cookie['name'], cookie['value'])
            
            # 添加常用请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            
            print("✅ 会话信息已提取到requests.Session对象")
            return session
            
        except Exception as e:
            print(f"[-] 提取会话信息失败: {e}")
            return None
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("[+] 浏览器已关闭")

def main():
    """主函数"""
    print("=" * 70)
    print("🚀 私域网站爬虫 - Selenium自动化解决方案 (集成OCR验证码识别)")
    print("=" * 70)

    # 检查OCR验证码识别功能状态
    if DDDDOCR_AVAILABLE:
        print("✅ ddddocr验证码识别功能已启用 - 将使用ddddocr自动识别验证码")
    elif PYTESSERACT_AVAILABLE:
        print("✅ pytesseract验证码识别功能已启用 - 将使用pytesseract自动识别验证码")
    else:
        print("⚠️  OCR验证码识别功能未启用 - 需要手动输入验证码")
        print("   安装依赖: pip install ddddocr 或 pip install pytesseract")

    print()

    # 创建登录实例
    login_bot = SeleniumLoginSolution(
        headless=False,         # 设置为True可后台运行
        ddddocr_enabled=True    # 启用OCR自动识别验证码
    )
    
    try:
        # 执行登录
        success = login_bot.login()
        
        if success:
            print("✅ 登录流程成功完成!")
            
            # 获取Cookies
            login_bot.get_cookies()
            
            # 提取会话信息
            session = login_bot.extract_session_for_requests()
            
            if session:
                print("🔧 现在可以使用session对象进行后续的数据抓取")
                print("示例: response = session.get('https://apollo.siyscrm.com/api/data')")
            
            # 保持浏览器打开一段时间供用户查看
            input("按Enter键关闭浏览器...")
            
        else:
            print("❌ 登录失败")
            
    except KeyboardInterrupt:
        print("\n[!] 用户中断操作")
    except Exception as e:
        print(f"[-] 程序执行出错: {e}")
    finally:
        login_bot.close()

if __name__ == "__main__":
    main()
