# OCR验证码自动识别功能

## 功能概述

本项目已集成OCR（光学字符识别）技术，可以自动识别验证码图片，大大提高登录自动化的效率。

## 主要特性

✅ **自动识别验证码** - 使用EasyOCR库自动识别验证码文本  
✅ **图像预处理** - 自动去噪、二值化、增强对比度提高识别率  
✅ **智能回退** - OCR识别失败时自动回退到手动输入模式  
✅ **多语言支持** - 支持中文和英文验证码识别  
✅ **高置信度筛选** - 只采用高置信度的识别结果  
✅ **错误纠正** - 自动修正常见的OCR识别错误  

## 安装依赖

### 方法1：自动安装（推荐）
```bash
python install_ocr_dependencies.py
```

### 方法2：手动安装
```bash
pip install easyocr opencv-python Pillow numpy
```

## 使用方法

### 基本使用
```python
from selenium_login_solution import SeleniumLoginSolution

# 创建登录实例，启用OCR功能
login_bot = SeleniumLoginSolution(
    headless=False,      # 是否无头模式
    ocr_enabled=True     # 启用OCR自动识别
)

# 执行登录（验证码将自动识别）
success = login_bot.login()
```

### 禁用OCR功能
```python
# 如果不想使用OCR，可以禁用
login_bot = SeleniumLoginSolution(ocr_enabled=False)
```

## OCR识别流程

1. **截取验证码图片** - 自动定位并截取验证码元素
2. **图像预处理** - 放大、增强对比度、去噪、二值化
3. **OCR识别** - 使用EasyOCR进行文字识别
4. **结果筛选** - 选择置信度最高的识别结果
5. **错误纠正** - 修正常见的字符识别错误
6. **自动填入** - 将识别结果填入验证码输入框

## 图像预处理技术

### 预处理步骤
- **尺寸调整** - 放大小尺寸图片提高识别率
- **对比度增强** - 增强图片对比度和锐度
- **灰度转换** - 转换为灰度图像
- **高斯模糊** - 去除图像噪点
- **自适应阈值** - 二值化处理
- **形态学操作** - 去除细小噪点

### 识别优化
- **多次尝试** - 最多尝试3次识别，选择最佳结果
- **置信度筛选** - 只采用置信度>0.3的结果
- **字符清理** - 移除非字母数字字符
- **常见错误修正** - O→0, I→1, l→1等

## 测试OCR功能

运行测试脚本验证OCR功能：
```bash
python test_ocr_captcha.py
```

测试脚本会：
- 创建模拟验证码图片
- 测试OCR识别准确率
- 输出详细的测试报告

## 配置参数

### OCR识别参数
```python
# 在recognize_captcha_with_ocr方法中可调整的参数
max_attempts = 3        # 最大识别尝试次数
confidence_threshold = 0.3  # 最低置信度阈值
high_confidence = 0.7   # 高置信度阈值（达到后直接返回）
```

### 图像预处理参数
```python
# 在preprocess_captcha_image方法中可调整的参数
contrast_factor = 2.0   # 对比度增强倍数
sharpness_factor = 2.0  # 锐度增强倍数
blur_kernel = (3, 3)    # 高斯模糊核大小
morph_kernel = (2, 2)   # 形态学操作核大小
```

## 故障排除

### 常见问题

**1. OCR库安装失败**
```bash
# 尝试升级pip
python -m pip install --upgrade pip
# 重新安装
pip install easyocr opencv-python
```

**2. 识别率低**
- 检查验证码图片质量
- 调整预处理参数
- 确认验证码语言设置

**3. 内存不足**
```python
# 禁用GPU加速
ocr_reader = easyocr.Reader(['en', 'ch_sim'], gpu=False)
```

### 日志调试

程序会输出详细的日志信息：
```
INFO - 开始OCR识别验证码: captcha_selenium_1234567890.png
INFO - 图片已放大: 80x30 -> 200x75
INFO - 图片预处理完成: captcha_selenium_1234567890_processed.png
INFO - OCR识别结果 (尝试1): 'A3B7', 置信度: 0.856
INFO - OCR识别成功: 'A3B7', 最终置信度: 0.856
```

## 性能优化建议

1. **首次运行** - EasyOCR首次运行会下载模型文件，需要网络连接
2. **内存使用** - OCR模型会占用一定内存，建议至少4GB RAM
3. **识别速度** - 单次识别通常需要2-5秒
4. **准确率** - 对于清晰的数字字母验证码，准确率可达80%以上

## 更新日志

### v2.0 - OCR集成版本
- ✅ 集成EasyOCR自动识别验证码
- ✅ 添加图像预处理提高识别率
- ✅ 实现智能回退机制
- ✅ 添加详细日志输出
- ✅ 支持中英文验证码
- ✅ 添加测试工具和安装脚本

### v1.0 - 基础版本
- ✅ Selenium自动化登录
- ✅ 手动验证码输入
- ✅ 反检测配置

## 技术支持

如果遇到问题，请：
1. 运行测试脚本检查OCR功能
2. 查看日志输出定位问题
3. 检查依赖库安装情况
4. 尝试调整预处理参数
