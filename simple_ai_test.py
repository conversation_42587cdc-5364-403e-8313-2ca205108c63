#!/usr/bin/env python3
"""
Simple AI captcha recognition test
"""

import base64
import os
from openai import OpenAI

def test_captcha():
    """Test AI captcha recognition"""
    
    image_path = "captcha_selenium_1753772999.png"
    
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return None
    
    print(f"Testing AI captcha recognition: {image_path}")
    
    try:
        # Initialize OpenRouter client
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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",
        )
        print("OpenRouter client initialized successfully")
        
        # Read image and convert to base64
        with open(image_path, 'rb') as image_file:
            image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        
        print(f"Image converted to base64, length: {len(image_base64)} characters")
        
        # Call AI model
        print("Calling AI model for captcha recognition...")
        
        completion = client.chat.completions.create(
            model="qwen/qwen3-235b-a22b-2507:free",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please identify the text in this captcha image. Return only the characters you see, no explanation."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=50,
            temperature=0.1
        )
        
        # Get recognition result
        ai_result = completion.choices[0].message.content.strip()
        print(f"AI recognition result: '{ai_result}'")
        
        # Clean result
        cleaned_result = ''.join(char for char in ai_result if char.isalnum())
        print(f"Cleaned result: '{cleaned_result}'")
        
        return cleaned_result
        
    except Exception as e:
        print(f"AI recognition failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=" * 50)
    print("AI Captcha Recognition Test")
    print("=" * 50)
    
    result = test_captcha()
    
    print("\n" + "=" * 50)
    print("Test Results")
    print("=" * 50)
    
    if result:
        print(f"Success: {result}")
        print(f"Length: {len(result)} characters")
    else:
        print("Failed")
