#!/usr/bin/env python3
"""
简单的验证码识别方案
使用基本的图像处理技术进行验证码识别
"""

from PIL import Image, ImageEnhance, ImageFilter
import numpy as np
import os

def simple_captcha_recognition(image_path):
    """
    简单的验证码识别
    Args:
        image_path: 验证码图片路径
    Returns:
        str: 识别结果
    """
    try:
        print(f"🔍 开始简单OCR识别: {image_path}")
        
        # 打开图片
        img = Image.open(image_path)
        
        # 转换为RGB
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # 放大图片
        width, height = img.size
        if width < 200:
            scale = 200 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            print(f"图片已放大: {width}x{height} -> {new_width}x{new_height}")
        
        # 增强对比度
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(2.0)
        
        # 转换为灰度
        img = img.convert('L')
        
        # 二值化
        threshold = 128
        img = img.point(lambda x: 255 if x > threshold else 0, mode='1')
        
        # 保存处理后的图片用于调试
        debug_path = image_path.replace('.png', '_processed.png')
        img.save(debug_path)
        print(f"处理后的图片已保存: {debug_path}")
        
        # 转换为numpy数组进行分析
        img_array = np.array(img)
        
        # 简单的字符分割和识别
        result = analyze_image_pattern(img_array)
        
        print(f"简单OCR识别结果: {result}")
        return result
        
    except Exception as e:
        print(f"简单OCR识别失败: {e}")
        return None

def analyze_image_pattern(img_array):
    """
    分析图像模式，尝试识别字符
    这是一个非常简单的实现，主要用于演示
    """
    try:
        height, width = img_array.shape
        
        # 查找字符区域
        # 这里使用非常简单的方法：统计每列的黑色像素
        col_sums = []
        for col in range(width):
            black_pixels = np.sum(img_array[:, col] == 0)  # 黑色像素为0
            col_sums.append(black_pixels)
        
        # 找到可能的字符边界
        char_boundaries = []
        in_char = False
        start_col = 0
        
        for col, black_count in enumerate(col_sums):
            if black_count > height * 0.1:  # 如果这一列有足够的黑色像素
                if not in_char:
                    start_col = col
                    in_char = True
            else:
                if in_char:
                    char_boundaries.append((start_col, col))
                    in_char = False
        
        if in_char:  # 处理最后一个字符
            char_boundaries.append((start_col, width))
        
        print(f"检测到 {len(char_boundaries)} 个可能的字符区域")
        
        # 简单的字符识别（这里只是示例，实际效果有限）
        result = ""
        for i, (start, end) in enumerate(char_boundaries):
            if end - start > 5:  # 忽略太窄的区域
                # 这里可以添加更复杂的字符识别逻辑
                # 现在只是简单地根据宽度猜测
                char_width = end - start
                if char_width < 15:
                    result += "1"
                elif char_width < 25:
                    result += "A"
                else:
                    result += "M"
        
        return result if result else "UNKN"
        
    except Exception as e:
        print(f"图像分析失败: {e}")
        return "ERR"

def test_simple_ocr():
    """测试简单OCR"""
    image_path = "captcha_selenium_1753772999.png"
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    print("=" * 60)
    print("🧪 简单验证码识别测试")
    print("=" * 60)
    
    result = simple_captcha_recognition(image_path)
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if result:
        print(f"✅ 识别结果: {result}")
        print("⚠️  注意: 这是一个非常简单的实现，准确率有限")
        print("建议使用专业的OCR库如ddddocr或pytesseract")
    else:
        print("❌ 识别失败")

if __name__ == "__main__":
    test_simple_ocr()
