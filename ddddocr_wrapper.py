#!/usr/bin/env python3
"""
ddddocr兼容性包装器
解决Pillow版本兼容性问题
"""

import os
from PIL import Image

def patch_pillow_compatibility():
    """修补Pillow兼容性问题"""
    # 如果新版Pillow没有ANTIALIAS，添加它
    if not hasattr(Image, 'ANTIALIAS'):
        Image.ANTIALIAS = Image.LANCZOS
        print("🔧 已修补Pillow兼容性问题")

def safe_ddddocr_import():
    """安全导入ddddocr"""
    try:
        # 先修补兼容性
        patch_pillow_compatibility()
        
        # 然后导入ddddocr
        import ddddocr
        return ddddocr, None
    except Exception as e:
        return None, str(e)

def test_ddddocr_with_wrapper(image_path):
    """使用包装器测试ddddocr"""
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return None
    
    print(f"🔍 使用包装器测试ddddocr识别: {image_path}")
    
    # 安全导入ddddocr
    ddddocr, error = safe_ddddocr_import()
    
    if ddddocr is None:
        print(f"❌ ddddocr导入失败: {error}")
        return None
    
    try:
        print("✅ ddddocr库加载成功（使用兼容性包装器）")
        
        # 初始化识别器
        ocr = ddddocr.DdddOcr()
        print("✅ ddddocr识别器初始化成功")
        
        # 读取图片
        with open(image_path, 'rb') as f:
            img_bytes = f.read()
        
        print(f"✅ 图片已读取，大小: {len(img_bytes)} 字节")
        
        # 识别验证码
        print("🤖 正在识别验证码...")
        result = ocr.classification(img_bytes)
        
        print(f"🎯 识别原始结果: '{result}'")
        
        # 清理结果
        cleaned_result = clean_result(result)
        print(f"✨ 清理后的结果: '{cleaned_result}'")
        
        return cleaned_result
        
    except Exception as e:
        print(f"❌ ddddocr识别失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def clean_result(text):
    """清理识别结果"""
    if not text:
        return ""
    
    # 移除空格和换行符
    text = text.strip().replace('\n', '').replace('\r', '').replace(' ', '')
    
    # 只保留字母数字
    cleaned = ''.join(char for char in text if char.isalnum())
    
    # 常见字符混淆修正
    replacements = {
        'O': '0', 'o': '0', 'I': '1', 'l': '1',
        'S': '5', 'G': '6', 'B': '8', 'Z': '2'
    }
    
    if len(cleaned) >= 3 and cleaned.isalnum():
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
    
    return cleaned.upper()

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 ddddocr兼容性包装器测试")
    print("=" * 60)
    
    # 测试指定的验证码图片
    image_path = "captcha_selenium_1753772999.png"
    
    result = test_ddddocr_with_wrapper(image_path)
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if result:
        print(f"✅ 识别成功: {result}")
        print(f"📏 结果长度: {len(result)} 字符")
        print(f"🔤 字符类型: {'数字' if result.isdigit() else '字母数字混合' if result.isalnum() else '包含特殊字符'}")
    else:
        print("❌ 识别失败")
    
    print("\n💡 提示:")
    print("- 此包装器解决了Pillow版本兼容性问题")
    print("- 如果仍有问题，建议使用pytesseract替代")

if __name__ == "__main__":
    main()
