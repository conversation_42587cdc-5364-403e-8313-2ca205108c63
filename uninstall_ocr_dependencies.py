#!/usr/bin/env python3
"""
阿里云OCR验证码识别依赖卸载脚本
自动卸载阿里云OCR验证码识别相关的Python库
"""

import subprocess
import sys
import os

def uninstall_package(package_name):
    """卸载Python包"""
    try:
        print(f"正在卸载 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", package_name, "-y"])
        print(f"✅ {package_name} 卸载成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 卸载失败: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name.replace("-", "_"))
        print(f"📦 {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🗑️  阿里云OCR验证码识别依赖卸载工具")
    print("=" * 60)
    
    # 需要卸载的包列表（与install_ocr_dependencies.py中的包对应）
    packages_to_uninstall = [
        ("requests", "Requests - HTTP请求库"),
        ("opencv-python", "OpenCV - 图像处理库"),
        ("Pillow", "PIL - Python图像库"),
        ("numpy", "NumPy - 数值计算库"),
    ]
    
    print("检查当前环境...")
    print()
    
    # 检查已安装的包
    installed_packages = []
    not_installed_packages = []
    
    for package, description in packages_to_uninstall:
        if check_package(package.replace("-", "_")):
            installed_packages.append((package, description))
        else:
            not_installed_packages.append((package, description))
    
    print()
    
    if not installed_packages:
        print("🎉 没有需要卸载的依赖包！")
        print("所有相关包都未安装或已经被卸载")
        return
    
    print(f"发现 {len(installed_packages)} 个已安装的依赖包:")
    for package, description in installed_packages:
        print(f"  - {package}: {description}")
    
    if not_installed_packages:
        print(f"\n以下 {len(not_installed_packages)} 个包未安装:")
        for package, description in not_installed_packages:
            print(f"  - {package}: {description}")
    
    print()
    
    # 询问用户是否继续卸载
    response = input("是否现在卸载这些依赖？(y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("卸载已取消")
        return
    
    print()
    print("开始卸载依赖...")
    print()
    
    # 卸载已安装的包
    success_count = 0
    for package, description in installed_packages:
        if uninstall_package(package):
            success_count += 1
        print()
    
    print("=" * 60)
    
    if success_count == len(installed_packages):
        print("🎉 所有依赖卸载完成！")
        print()
        print("已卸载的包:")
        for package, description in installed_packages:
            print(f"  ✅ {package}")
        print()
        print("注意事项:")
        print("  - 这些库可能被其他程序使用")
        print("  - 如需重新安装，请运行: python install_ocr_dependencies.py")
        print("  - 卸载后验证码识别功能将不可用")
    else:
        failed_count = len(installed_packages) - success_count
        print(f"⚠️  {failed_count} 个依赖卸载失败")
        print("请检查权限或手动卸载失败的依赖")
        print()
        print("手动卸载命令:")
        for package, _ in installed_packages:
            print(f"  pip uninstall {package} -y")
    
    print()
    print("💡 提示:")
    print("  - requests库是Python常用库，卸载可能影响其他程序")
    print("  - numpy和Pillow也是常用的数据处理库")
    print("  - 建议只在确定不需要时才卸载")

if __name__ == "__main__":
    main()
