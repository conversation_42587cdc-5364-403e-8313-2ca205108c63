#!/usr/bin/env python3
"""
ddddocr验证码识别测试脚本
"""

import os

def test_ddddocr_captcha(image_path):
    """
    测试ddddocr识别验证码
    Args:
        image_path: 验证码图片路径
    Returns:
        str: 识别结果
    """
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return None
    
    print(f"🔍 开始测试ddddocr识别验证码: {image_path}")
    
    try:
        # 导入ddddocr
        print("🔄 正在导入ddddocr库...")
        import ddddocr
        print("✅ ddddocr库加载成功")
        print(f"📦 ddddocr版本: {ddddocr.__version__ if hasattr(ddddocr, '__version__') else '未知'}")
        
        # 初始化识别器
        ocr = ddddocr.DdddOcr()
        print("✅ ddddocr识别器初始化成功")
        
        # 读取图片
        with open(image_path, 'rb') as f:
            img_bytes = f.read()
        
        print(f"✅ 图片已读取，大小: {len(img_bytes)} 字节")
        
        # 识别验证码
        print("🤖 正在识别验证码...")
        result = ocr.classification(img_bytes)
        
        print(f"🎯 识别原始结果: '{result}'")
        
        # 清理结果
        cleaned_result = clean_result(result)
        print(f"✨ 清理后的结果: '{cleaned_result}'")
        
        return cleaned_result
        
    except ImportError as e:
        print(f"❌ ddddocr库导入失败: {e}")
        print("可能的解决方案:")
        print("1. 重新安装: pip uninstall ddddocr -y && pip install ddddocr")
        print("2. 使用指定Python: python -m pip install ddddocr")
        print("3. 检查Python环境: python -c \"import sys; print(sys.executable)\"")
        return None
    except Exception as e:
        print(f"❌ ddddocr识别失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def clean_result(text):
    """清理识别结果"""
    if not text:
        return ""
    
    # 移除空格和换行符
    text = text.strip().replace('\n', '').replace('\r', '').replace(' ', '')
    
    # 只保留字母数字
    cleaned = ''.join(char for char in text if char.isalnum())
    
    # 常见字符混淆修正
    replacements = {
        'O': '0', 'o': '0', 'I': '1', 'l': '1',
        'S': '5', 'G': '6', 'B': '8', 'Z': '2'
    }
    
    if len(cleaned) >= 3 and cleaned.isalnum():
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
    
    return cleaned.upper()

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 ddddocr验证码识别测试工具")
    print("=" * 60)
    
    # 测试指定的验证码图片
    image_path = "captcha_selenium_1753772999.png"
    
    result = test_ddddocr_captcha(image_path)
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if result:
        print(f"✅ 识别成功: {result}")
        print(f"📏 结果长度: {len(result)} 字符")
        print(f"🔤 字符类型: {'数字' if result.isdigit() else '字母数字混合' if result.isalnum() else '包含特殊字符'}")
    else:
        print("❌ 识别失败")
    
    print("\n💡 提示:")
    print("- ddddocr是专门的验证码识别库")
    print("- 对常见验证码有很好的识别效果")
    print("- 无需配置，开箱即用")
    print("- 如果识别不准确，可以尝试图像预处理")

if __name__ == "__main__":
    main()
