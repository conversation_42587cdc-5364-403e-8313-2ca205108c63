#!/usr/bin/env python3
"""
修复ddddocr与新版Pillow的兼容性问题
"""

import os
import sys

def fix_ddddocr_pillow_compatibility():
    """修复ddddocr与Pillow的兼容性问题"""
    
    print("🔧 修复ddddocr与Pillow兼容性问题...")
    
    try:
        # 找到ddddocr的安装路径
        import ddddocr
        ddddocr_path = os.path.dirname(ddddocr.__file__)
        init_file = os.path.join(ddddocr_path, "__init__.py")
        
        print(f"📁 ddddocr安装路径: {ddddocr_path}")
        
        if not os.path.exists(init_file):
            print("❌ 找不到ddddocr的__init__.py文件")
            return False
        
        # 读取文件内容
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否需要修复
        if 'Image.ANTIALIAS' not in content:
            print("✅ ddddocr已经兼容新版Pillow，无需修复")
            return True
        
        print("🔄 正在修复兼容性问题...")
        
        # 替换ANTIALIAS为LANCZOS
        fixed_content = content.replace('Image.ANTIALIAS', 'Image.LANCZOS')
        
        # 备份原文件
        backup_file = init_file + '.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 原文件已备份到: {backup_file}")
        
        # 写入修复后的内容
        with open(init_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("✅ 修复完成！")
        print("🔄 请重新运行测试脚本")
        
        return True
        
    except ImportError:
        print("❌ ddddocr未安装")
        return False
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_fix():
    """测试修复是否成功"""
    print("\n🧪 测试修复结果...")
    
    try:
        import ddddocr
        ocr = ddddocr.DdddOcr()
        print("✅ ddddocr初始化成功，修复有效！")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 ddddocr兼容性修复工具")
    print("=" * 60)
    
    # 检查当前问题
    try:
        import ddddocr
        ocr = ddddocr.DdddOcr()
        print("✅ ddddocr工作正常，无需修复")
        return
    except AttributeError as e:
        if "ANTIALIAS" in str(e):
            print("🔍 检测到Pillow兼容性问题")
        else:
            print(f"❌ 其他错误: {e}")
            return
    except Exception as e:
        print(f"❌ 无法加载ddddocr: {e}")
        return
    
    # 执行修复
    if fix_ddddocr_pillow_compatibility():
        # 测试修复结果
        test_fix()
    
    print("\n💡 如果修复失败，可以尝试:")
    print("1. pip install Pillow==9.5.0")
    print("2. pip uninstall ddddocr -y && pip install ddddocr==1.4.11")

if __name__ == "__main__":
    main()
