#!/usr/bin/env python3
"""
AI验证码自动识别使用示例
演示如何使用集成AI大模型验证码识别功能的登录解决方案
"""

from selenium_login_solution import SeleniumLoginSolution
import time

def example_with_ai_ocr():
    """使用AI大模型自动识别验证码的示例"""
    print("=" * 60)
    print("🤖 示例1: 启用AI大模型自动识别验证码")
    print("=" * 60)

    # 创建登录实例，启用AI验证码识别功能
    login_bot = SeleniumLoginSolution(
        headless=False,        # 显示浏览器窗口，便于观察
        ai_ocr_enabled=True    # 启用AI大模型自动识别验证码
    )
    
    try:
        # 执行登录
        print("开始自动登录...")
        success = login_bot.login(
            username="13995933053",  # 替换为您的用户名
            password="aabb6688"      # 替换为您的密码
        )
        
        if success:
            print("✅ 登录成功！")
            
            # 获取登录后的cookies
            cookies = login_bot.get_cookies()
            
            # 提取会话信息用于后续请求
            session = login_bot.extract_session_for_requests()
            
            if session:
                print("🔧 会话信息已提取，可用于后续数据抓取")
            
            # 保持浏览器打开一段时间
            input("按Enter键继续...")
            
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 执行过程出错: {e}")
    finally:
        login_bot.close()

def example_without_ai_ocr():
    """不使用AI识别，手动输入验证码的示例"""
    print("=" * 60)
    print("👤 示例2: 禁用AI识别，手动输入验证码")
    print("=" * 60)

    # 创建登录实例，禁用AI验证码识别功能
    login_bot = SeleniumLoginSolution(
        headless=False,        # 显示浏览器窗口
        ai_ocr_enabled=False   # 禁用AI识别，使用手动输入
    )
    
    try:
        # 执行登录
        print("开始登录（需要手动输入验证码）...")
        success = login_bot.login()
        
        if success:
            print("✅ 登录成功！")
            input("按Enter键继续...")
        else:
            print("❌ 登录失败")
            
    except Exception as e:
        print(f"❌ 执行过程出错: {e}")
    finally:
        login_bot.close()

def example_headless_mode():
    """无头模式示例（后台运行）"""
    print("=" * 60)
    print("🔇 示例3: 无头模式 + AI自动识别")
    print("=" * 60)

    # 创建登录实例，无头模式 + AI识别
    login_bot = SeleniumLoginSolution(
        headless=True,         # 无头模式，后台运行
        ai_ocr_enabled=True    # 启用AI大模型自动识别
    )
    
    try:
        print("后台模式登录中...")
        success = login_bot.login()
        
        if success:
            print("✅ 后台登录成功！")
            
            # 在无头模式下，可以直接进行数据抓取
            session = login_bot.extract_session_for_requests()
            if session:
                print("🔧 可以开始数据抓取了")
                # 这里可以添加您的数据抓取逻辑
                
        else:
            print("❌ 后台登录失败")
            
    except Exception as e:
        print(f"❌ 执行过程出错: {e}")
    finally:
        login_bot.close()

def main():
    """主函数"""
    print("🚀 AI验证码自动识别 - 使用示例")
    print()

    # 检查AI验证码识别功能状态
    try:
        from openai import OpenAI
        print("✅ AI验证码识别功能可用")
    except ImportError:
        print("⚠️  AI验证码识别功能不可用，请先安装依赖:")
        print("   python install_ocr_dependencies.py")
        print()

    while True:
        print("\n请选择示例:")
        print("1. 启用AI大模型自动识别验证码")
        print("2. 禁用AI识别，手动输入验证码")
        print("3. 无头模式 + AI自动识别")
        print("0. 退出")

        choice = input("\n请输入选择 (0-3): ").strip()

        if choice == "1":
            example_with_ai_ocr()
        elif choice == "2":
            example_without_ai_ocr()
        elif choice == "3":
            example_headless_mode()
        elif choice == "0":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
