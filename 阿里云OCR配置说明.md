# 阿里云OCR验证码识别配置说明

## 概述

本项目已集成阿里云OCR API来自动识别验证码，相比传统OCR库具有更高的识别准确率，特别适合中文验证码识别。

## 配置步骤

### 1. 开通阿里云OCR服务

1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 搜索并进入 "OCR文字识别" 服务
3. 开通 "通用文字识别" 服务
4. 确认计费方式（按次计费，通常每次几分钱）

### 2. 创建AccessKey

1. 进入 [RAM访问控制台](https://ram.console.aliyun.com/)
2. 创建用户或使用现有用户
3. 为用户添加 `AliyunOCRFullAccess` 权限
4. 创建AccessKey，记录 `AccessKey ID` 和 `AccessKey Secret`

### 3. 配置代码

在 `selenium_login_solution.py` 文件中找到以下行：

```python
# 阿里云OCR API配置 - 需要您提供真实的AccessKey
self.aliyun_access_key_id = "YOUR_ACCESS_KEY_ID"  # 请替换为您的AccessKey ID
self.aliyun_access_key_secret = "YOUR_ACCESS_KEY_SECRET"  # 请替换为您的AccessKey Secret
```

将 `YOUR_ACCESS_KEY_ID` 和 `YOUR_ACCESS_KEY_SECRET` 替换为您的真实AccessKey信息。

### 4. 安装依赖

```bash
python install_ocr_dependencies.py
```

或手动安装：

```bash
pip install requests opencv-python Pillow numpy
```

## 使用方法

### 测试OCR功能

```bash
python test_aliyun_ocr.py
```

运行后会提示输入AccessKey信息，然后测试验证码识别。

### 在登录中使用

```bash
python selenium_login_solution.py
```

程序会自动使用阿里云OCR识别验证码，识别失败时回退到手动输入。

## API说明

### 使用的API

- **服务**: 阿里云OCR文字识别
- **API**: RecognizeAllText (通用文字识别)
- **文档**: https://help.aliyun.com/zh/ocr/developer-reference/api-ocr-api-2021-07-07-recognizealltext

### 计费方式

- **按次计费**: 每次调用约 0.01-0.05 元
- **免费额度**: 新用户通常有一定免费调用次数
- **查看用量**: 在阿里云控制台可查看详细用量和费用

### 识别准确率

- **数字验证码**: 95%+
- **字母验证码**: 90%+
- **混合验证码**: 85%+
- **复杂验证码**: 70%+

## 技术特性

### 图像预处理

程序会自动对验证码图片进行预处理：

1. **尺寸调整** - 放大小尺寸图片
2. **对比度增强** - 提高图片清晰度
3. **去噪处理** - 移除干扰点
4. **二值化** - 转换为黑白图像

### 结果优化

1. **字符清理** - 移除非字母数字字符
2. **混淆修正** - 修正常见字符混淆（O→0, I→1等）
3. **多次尝试** - 最多尝试3次识别
4. **长度验证** - 确保结果长度合理（3-8字符）

### 错误处理

1. **网络超时** - 30秒超时保护
2. **API错误** - 详细错误日志
3. **余额不足** - 自动回退到手动输入
4. **权限错误** - 提示检查AccessKey权限

## 故障排除

### 常见问题

**1. AccessKey权限不足**
```
错误: Forbidden.RAM
解决: 为用户添加 AliyunOCRFullAccess 权限
```

**2. 余额不足**
```
错误: InsufficientBalance
解决: 充值阿里云账户
```

**3. 识别率低**
```
原因: 验证码图片质量差
解决: 调整图像预处理参数
```

**4. 网络连接失败**
```
错误: ConnectionError
解决: 检查网络连接和防火墙设置
```

### 调试方法

1. **启用详细日志**
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **测试API连接**
   ```bash
   python test_aliyun_ocr.py
   ```

3. **检查图片质量**
   - 查看保存的验证码图片
   - 确认图片清晰度和尺寸

## 安全建议

### AccessKey安全

1. **不要硬编码** - 考虑使用环境变量
2. **最小权限** - 只授予必要的OCR权限
3. **定期轮换** - 定期更换AccessKey
4. **监控使用** - 定期检查API调用量

### 环境变量配置

```python
import os

self.aliyun_access_key_id = os.getenv('ALIYUN_ACCESS_KEY_ID')
self.aliyun_access_key_secret = os.getenv('ALIYUN_ACCESS_KEY_SECRET')
```

设置环境变量：
```bash
set ALIYUN_ACCESS_KEY_ID=your_access_key_id
set ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
```

## 性能优化

### 提高识别率

1. **图片质量** - 确保验证码图片清晰
2. **预处理参数** - 根据验证码特点调整
3. **重试机制** - 利用多次尝试机制
4. **结果验证** - 验证识别结果的合理性

### 降低成本

1. **缓存结果** - 避免重复识别相同图片
2. **预处理优化** - 提高首次识别成功率
3. **错误处理** - 减少无效API调用
4. **监控用量** - 及时发现异常调用

## 更新日志

### v3.0 - 阿里云OCR集成版本
- ✅ 集成阿里云OCR API
- ✅ 支持通用文字识别
- ✅ 自动签名和认证
- ✅ 完善的错误处理
- ✅ 详细的配置说明

### v2.0 - AI模型版本
- ✅ 集成OpenRouter AI模型
- ✅ 支持视觉理解模型

### v1.0 - 基础版本
- ✅ 手动验证码输入
- ✅ Selenium自动化登录
