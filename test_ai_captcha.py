#!/usr/bin/env python3
"""
测试AI大模型识别验证码
"""

import base64
import os
from openai import OpenAI

def test_ai_captcha_recognition(image_path):
    """测试AI识别验证码"""
    
    # 检查图片文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return None
    
    print(f"🔍 开始测试AI识别验证码: {image_path}")
    
    try:
        # 初始化OpenRouter客户端
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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",
        )
        print("✅ OpenRouter客户端初始化成功")
        
        # 读取图片并转换为base64
        with open(image_path, 'rb') as image_file:
            image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        
        print(f"✅ 图片已转换为base64，长度: {len(image_base64)} 字符")
        
        # 调用AI模型
        print("🤖 正在调用AI模型识别验证码...")
        
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "https://github.com/captcha-solver",
                "X-Title": "Captcha Recognition Tool",
            },
            model="google/gemini-flash-1.5:free",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please identify the text in this captcha image. Captcha is usually a 4-6 character combination of numbers and letters. Please return only the recognized characters without any other text or explanation."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=50,
            temperature=0.1
        )
        
        # 获取识别结果
        ai_result = completion.choices[0].message.content.strip()
        print(f"🎯 AI识别原始结果: '{ai_result}'")
        
        # 清理结果
        cleaned_result = clean_ai_result(ai_result)
        print(f"✨ 清理后的结果: '{cleaned_result}'")
        
        return cleaned_result
        
    except Exception as e:
        print(f"❌ AI识别失败: {e}")
        return None

def clean_ai_result(text):
    """清理AI识别结果"""
    if not text:
        return ""
    
    # 移除常见的AI回复前缀和后缀
    text = text.strip()
    
    # 移除可能的解释性文字
    common_prefixes = [
        "验证码是", "验证码为", "识别结果是", "识别结果为", 
        "图片中的验证码是", "图片中的文字是", "验证码：", "结果：",
        "The captcha is", "The code is", "Result:", "Code:"
    ]
    
    for prefix in common_prefixes:
        if text.startswith(prefix):
            text = text[len(prefix):].strip()
    
    # 移除标点符号和空格，只保留字母数字
    cleaned = ''.join(char for char in text if char.isalnum())
    
    # 常见字符混淆修正
    replacements = {
        'O': '0',  # 字母O替换为数字0
        'o': '0',  # 小写o替换为数字0
        'I': '1',  # 字母I替换为数字1
        'l': '1',  # 小写l替换为数字1
        'S': '5',  # 字母S有时会被误识别
        'G': '6',  # 字母G有时会被误识别
        'B': '8',  # 字母B有时会被误识别
        'Z': '2',  # 字母Z有时会被误识别为2
    }
    
    # 应用替换规则（仅当结果看起来像验证码时）
    if len(cleaned) >= 3 and cleaned.isalnum():
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
    
    return cleaned.upper()  # 转换为大写保持一致性

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 AI验证码识别测试工具")
    print("=" * 60)
    
    # 测试指定的验证码图片
    image_path = "captcha_selenium_1753772999.png"
    
    result = test_ai_captcha_recognition(image_path)
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    if result:
        print(f"✅ 识别成功: {result}")
        print(f"📏 结果长度: {len(result)} 字符")
        print(f"🔤 字符类型: {'数字' if result.isdigit() else '字母数字混合' if result.isalnum() else '包含特殊字符'}")
    else:
        print("❌ 识别失败")
    
    print("\n💡 提示:")
    print("- 如果识别失败，可能是网络问题或API限制")
    print("- 如果识别结果不准确，可以调整图像预处理参数")
    print("- 验证码图片质量会影响识别准确率")

if __name__ == "__main__":
    main()
